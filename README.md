# UCD Professional Academy Website

## Development Setup

### Quick Start

To start the development environment with the correct Node.js version:

```bash
make dev
```

This command:

1. Automatically switches to Node.js 16.19.1 using nvm
2. Runs `npm run dev` which starts the development server with:
   - Flash watch server
   - JSX file watcher and compiler
   - Tailwind CSS watcher and compiler

### Requirements

- [nvm](https://github.com/nvm-sh/nvm) must be installed
- Node.js 16.19.1 must be available via nvm

## Build Process

This project uses Flash 2024's script hooks to automatically handle JSX and Tailwind CSS compilation during Flash commands.

### Script Hooks Configuration

The build automation is configured in `config.js`:

- **`prebuild`**: Compiles JSX and Tailwind CSS before Flash builds
- **`prewatch`**: Ensures assets are compiled before starting development server

This eliminates manual build step coordination and ensures consistency across environments.

## SEO Implementation Documentation

This document explains how SEO titles and descriptions are generated for the UCD Professional Academy website.

### For Marketing Personnel

#### How to Set SEO Metadata in Storyblok

SEO metadata can be configured at multiple levels in the system:

1. **Global Default SEO (applies to all pages)**

   - Located in Settings > SEO section in Storyblok
   - Set `seo_default_title` and `seo_default_description`
   - These will be used when no page-specific SEO is set

2. **Page-Specific SEO (highest priority)**

   - Each page has an "SEO Metatags" section
   - These settings override all other SEO settings
   - Always fill in both Title and Description fields

3. **Banner Category SEO (for Creative Resources pages only)**
   - When editing banner categories in the "Creative Resources" component
   - Each category can have its own SEO title and description
   - These will be used on category-specific pages

#### Special Placeholders

- Use `[page]` in any SEO title to include the page title automatically
  - Example: "Learn About [page] | UCD Professional Academy"
  - Result on "Data Science" page: "Learn About Data Science | UCD Professional Academy"

#### Important Notes

- SEO fields are required - not providing them will cause errors
- "Homepage" is automatically replaced with "Home" in page titles

### For Developers

#### SEO Implementation Details

The SEO implementation is located in `snippets/seo.html` and follows this order of precedence:

1. Start with default title and description from `settings.json`
2. If it's a Creative Resources page with a matching banner category, use its SEO data
3. If page-specific SEO metatags exist, they override everything else
4. Replace any `[page]` placeholder with the actual page title
5. Throw an error if final SEO title or description is empty

## E2E Testing

The project includes End-to-End (E2E) testing to validate key functionality before merging code from dev to main branch.

### Project Structure

This repository contains two separate Node.js projects:

1. **Main Website (Root Directory)**

   - Uses Node 16.19.1 (specified in `.nvmrc`)
   - Dependencies and configuration in root `package.json`
   - Required for compatibility with existing infrastructure

2. **Test Suite (`tests/` Directory)**
   - Uses Node 22+ (specified in `tests/.nvmrc`)
   - Independent `package.json` with its own dependencies
   - Contains E2E tests using Puppeteer

This dual-project structure allows the main codebase to remain on Node 16 for compatibility while enabling modern testing functionality with Node 22.

### Test Configuration

Tests are defined in `tests/blocksToTest.json` with the following structure:

```json
[
  {
    "name": "Homepage - Banner",
    "path": "/professionalacademy/",
    "keywordToVerify": "Explore Courses"
  }
]
```

Each test:

1. Navigates to the specified path on the preview environment
2. Waits for the page to fully load
3. Checks if the specified keyword exists on the page
4. Generates a PDF of the page

### Running Tests Locally

1. **Prerequisites:**

   - [nvm](https://github.com/nvm-sh/nvm) installed (for automatic Node version switching)
   - Node 16.19.1 for main codebase
   - Node 22+ for tests

2. Create a `.env` file in the `tests/` directory based on `.env.example` with your:

   - `PREVIEW_URL`: URL of the preview environment to test
   - `BROWSERLESS_API_KEY`: API key for Browserless.io

3. Run the tests:
   ```
   npm test
   ```
   This uses a Makefile that automatically:
   - Switches to Node 22 using nvm
   - Installs test dependencies
   - Runs the tests
   - Switches back to Node 16

### Development Commands

- **Start development server with correct Node version:**

  ```
  make dev
  ```

- **Run tests:**
  ```
  make test
  ```

### GitHub Actions Integration

E2E tests automatically run on GitHub Actions using Node 22:

1. Checks for required environment variables
2. Waits for the preview environment to be ready (polls until 200 OK)
3. Sets up Node 22 for testing
4. Installs dependencies and runs tests in the `tests/` directory
5. Posts results as a comment on the PR
6. Fails the workflow if any test fails, preventing merge to main

### Customizing Tests

To add or modify tests, edit `tests/blocksToTest.json`. Each test requires:

- `name`: Descriptive name for the test
- `path`: URL path to test (appended to `PREVIEW_URL`)
- `keywordToVerify`: Text to check for on the page

### Adding New Test Dependencies

To add dependencies to the test suite:

```
cd tests
nvm use   # Switch to Node 22
npm install some-package --save
```

Remember that test dependencies should be added to `tests/package.json`, not the root `package.json`.

# Preact/JSX Components in Flash Storyblok Blocks

This document outlines how Preact/JSX components are integrated and used within specific Storyblok blocks in the Flash framework. These components are registered as HTML Custom Elements (custom tags like `<example-component>`) and are bundled into `professionalacademy/assets/js/jsx.min.js`.

**Key External Global Dependencies Overview:**

Preact components often rely on specific data being available in the global JavaScript scope (e.g., `window.courses`, `window.pageName`). The Storyblok block templates (`view.html` files) are responsible for ensuring these JavaScript variables are populated. These templates operate within the Flash EJS environment, which provides a suite of helper functions (e.g., `plugins.blocks()`, `plugins.readJSONFile()`, `plugins.richText()`) for accessing Storyblok data and performing common templating tasks.

## Arlo Course Data Integration

### Server-Side Data Fetching

Arlo course data is fetched at build time using Flash script hooks and saved to `data/arloCourses.json`.

#### How It Works

1. **Build Time**: The `postfetch` script hook runs `scripts/fetchArloCourses.mjs` after Storyblok content is fetched
2. **Data Storage**: Course data is saved to `data/arloCourses.json`
3. **EJS Access**: Templates can load the JSON directly using `plugins.readJSONFile('data/arloCourses.json')`
4. **JSX Access**: Existing client-side components continue to work unchanged

#### Using in EJS Templates

```html
<!-- Load Arlo courses data -->
<% const arloCourses = plugins.readJSONFile('data/arloCourses.json'); %>

<!-- Filter by template codes -->
<% const specificCourses = arloCourses.filter(course => ['DM-OD', 'HR-OD'].includes(course.TemplateCode)); %>

<!-- Display course information -->
<% specificCourses.forEach(course => { %>
<div>
  <h3><%= course.Name %></h3>
  <p><%= course.AdvertisedDuration %></p>
</div>
<% }); %>
```

## Tailwind CSS

### In Block Templates

You can use Tailwind CSS utility classes directly in your block templates:

```html
<div class="bg-primary text-white p-4 rounded-lg shadow-md">
  <h2 class="text-2xl font-bold mb-2">Hello Tailwind</h2>
  <p class="text-sm">This is a sample component using Tailwind CSS.</p>
</div>
```

### File Structure

- `tailwind.config.js`: Configuration file for Tailwind CSS
- `postcss.config.js`: Configuration file for PostCSS (used by Tailwind)
- `assets/scss/tailwind.scss`: Main Tailwind CSS entry file
- `professionalacademy/assets/css/tailwind.min.css`: Compiled Tailwind CSS file

### Best Practices

1. **Use Tailwind for new components**: For new components, prefer using Tailwind utility classes.
2. **Gradual adoption**: Existing components can continue to use the current SCSS approach.
3. **Avoid mixing styles**: Try not to mix traditional SCSS and Tailwind for the same element to prevent conflicts.
4. **Responsive design**: Use Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, etc.) which are configured to match the project's breakpoints.
